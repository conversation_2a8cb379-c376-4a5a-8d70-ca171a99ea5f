import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:image_picker/image_picker.dart';
import 'package:file_picker/file_picker.dart';
import 'package:mime/mime.dart';
import 'package:flutter/foundation.dart';
import 'dart:io';
import 'package:tolk/models/chat_models.dart';
import 'package:tolk/models/user_model.dart';
import 'package:tolk/services/notification_service.dart';
import 'package:tolk/services/encryption_service.dart';

class ChatService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final FirebaseStorage _storage = FirebaseStorage.instance;
  final NotificationService _notificationService = NotificationService();
  final EncryptionService _encryptionService = EncryptionService();

  // Collection references
  final CollectionReference _usersCollection = FirebaseFirestore.instance
      .collection('users');
  final CollectionReference _chatRoomsCollection = FirebaseFirestore.instance
      .collection('chatRooms');
  final CollectionReference _messagesCollection = FirebaseFirestore.instance
      .collection('messages');

  // Constructor
  ChatService() {
    _encryptionService.initialize();
  }

  // Get current user ID
  String get currentUserId => _auth.currentUser?.uid ?? '';

  // Update user online status
  Future<void> updateUserOnlineStatus(bool isOnline) async {
    if (currentUserId.isEmpty) return;

    await _usersCollection.doc(currentUserId).update({
      'isOnline': isOnline,
      'lastSeen': isOnline ? null : FieldValue.serverTimestamp(),
    });
  }

  // Get user by ID
  Future<UserModel?> getUserById(String userId) async {
    try {
      DocumentSnapshot doc = await _usersCollection.doc(userId).get();
      if (doc.exists) {
        return UserModel.fromMap(doc.data() as Map<String, dynamic>);
      }
      return null;
    } catch (e) {
      debugPrint('Error getting user: $e');
      return null;
    }
  }

  // Get all users (for user search)
  Stream<List<UserModel>> getAllUsers() {
    return _usersCollection
        .where('uid', isNotEqualTo: currentUserId)
        .snapshots()
        .map((snapshot) {
          return snapshot.docs
              .map(
                (doc) => UserModel.fromMap(doc.data() as Map<String, dynamic>),
              )
              .toList();
        });
  }

  // Upload group image and return download URL
  Future<String> uploadGroupImage(String chatRoomId, XFile image) async {
    final ref = _storage
        .ref()
        .child('group_images')
        .child(
          '${chatRoomId}_${DateTime.now().millisecondsSinceEpoch}_${image.name}',
        );
    final uploadTask = ref.putFile(File(image.path));
    await uploadTask;
    return await ref.getDownloadURL();
  }

  // Update group image for a chat room
  Future<void> updateGroupImage(String chatRoomId, String imageUrl) async {
    await _chatRoomsCollection.doc(chatRoomId).update({'groupImage': imageUrl});
  }

  // Update group name for a chat room
  Future<void> updateGroupName(String chatRoomId, String newName) async {
    await _chatRoomsCollection.doc(chatRoomId).update({'groupName': newName});
  }

  // Create a group chat room and return its ID
  Future<String> createGroupChatRoom({
    required String groupName,
    required List<String> participantIds,
    String? groupImage,
  }) async {
    final docRef = await _chatRoomsCollection.add({
      'participants': participantIds,
      'isGroupChat': true,
      'groupName': groupName,
      'groupImage': groupImage,
      'unreadCount': {for (var id in participantIds) id: 0},
      'createdAt': FieldValue.serverTimestamp(),
      'createdBy': currentUserId,
      'lastMessage': null,
      'lastMessageTime': null,
    });
    return docRef.id;
  }

  // Add participants to a group chat
  Future<void> addParticipantsToGroup(
    String chatRoomId,
    List<String> participantIds,
  ) async {
    if (chatRoomId.isEmpty || participantIds.isEmpty) return;

    try {
      final chatRoomDoc = await _chatRoomsCollection.doc(chatRoomId).get();
      if (!chatRoomDoc.exists) {
        print('💬 [ADD_PARTICIPANTS] Chat room does not exist!');
        return;
      }

      final chatRoom = ChatRoom.fromMap(
        chatRoomDoc.data() as Map<String, dynamic>,
        chatRoomId,
      );
      List<String> currentParticipants = List.from(chatRoom.participants);
      Map<String, int> currentUnreadCount = Map.from(chatRoom.unreadCount);

      // Add new participants and initialize unread count
      for (String participantId in participantIds) {
        if (!currentParticipants.contains(participantId)) {
          currentParticipants.add(participantId);
          currentUnreadCount[participantId] = 0;
        }
      }

      await _chatRoomsCollection.doc(chatRoomId).update({
        'participants': currentParticipants,
        'unreadCount': currentUnreadCount,
      });
      print('💬 [ADD_PARTICIPANTS] Participants added successfully!');

      // Send system messages for added participants
      final currentUser = await getUserById(currentUserId);
      if (currentUser != null) {
        for (String participantId in participantIds) {
          final addedUser = await getUserById(participantId);
          if (addedUser != null) {
            final messageText =
                '${currentUser.name ?? currentUser.phoneNumber} have added ${addedUser.name ?? addedUser.phoneNumber}';
            // Send message to the added user so they see it in their chat history
            await sendSystemMessage(
              chatRoomId: chatRoomId,
              text: messageText,
              receiverId: participantId,
            );
          }
        }
      }
    } catch (e) {
      print('💬 [ADD_PARTICIPANTS] Error adding participants: $e');
      throw Exception('Failed to add participants: $e');
    }
  }

  // Remove a participant from a group chat
  Future<void> removeParticipantFromGroup(
    String chatRoomId,
    String participantId,
  ) async {
    if (chatRoomId.isEmpty || participantId.isEmpty) return;

    try {
      final chatRoomDoc = await _chatRoomsCollection.doc(chatRoomId).get();
      if (!chatRoomDoc.exists) {
        print('💬 [REMOVE_PARTICIPANT] Chat room does not exist!');
        return;
      }

      final chatRoom = ChatRoom.fromMap(
        chatRoomDoc.data() as Map<String, dynamic>,
        chatRoomId,
      );
      List<String> currentParticipants = List.from(chatRoom.participants);
      Map<String, int> currentUnreadCount = Map.from(chatRoom.unreadCount);

      // Get user info before removing
      final currentUser = await getUserById(currentUserId);
      final removedUser = await getUserById(participantId);

      // Remove participant
      currentParticipants.remove(participantId);
      currentUnreadCount.remove(participantId);

      await _chatRoomsCollection.doc(chatRoomId).update({
        'participants': currentParticipants,
        'unreadCount': currentUnreadCount,
      });
      print('💬 [REMOVE_PARTICIPANT] Participant removed successfully!');

      // Send system message for removed participant
      if (currentUser != null && removedUser != null) {
        final messageText =
            participantId == currentUserId
                ? '${currentUser.name ?? currentUser.phoneNumber} has left the group' // Message for leaving
                : '${currentUser.name ?? currentUser.phoneNumber} has removed ${removedUser.name ?? removedUser.phoneNumber}'; // Message for removal

        // Send message to the user who performed the action (or the user who left)
        await sendSystemMessage(
          chatRoomId: chatRoomId,
          text: messageText,
          receiverId: currentUserId,
        );
      }
    } catch (e) {
      print('💬 [REMOVE_PARTICIPANT] Error removing participant: $e');
      throw Exception('Failed to remove participant: $e');
    }
  }

  // Create or get existing chat room
  Future<String> createOrGetChatRoom(String otherUserId) async {
    if (currentUserId.isEmpty) return '';

    // Check if chat room already exists
    QuerySnapshot query =
        await _chatRoomsCollection
            .where('participants', arrayContains: currentUserId)
            .where('isGroupChat', isEqualTo: false)
            .get();

    for (var doc in query.docs) {
      List<String> participants = List<String>.from(doc['participants']);
      if (participants.contains(otherUserId) && participants.length == 2) {
        return doc.id;
      }
    }

    // Create new chat room
    DocumentReference chatRoomRef = await _chatRoomsCollection.add({
      'participants': [currentUserId, otherUserId],
      'isGroupChat': false,
      'unreadCount': {currentUserId: 0, otherUserId: 0},
      'createdAt': FieldValue.serverTimestamp(),
      'createdBy': currentUserId,
    });

    return chatRoomRef.id;
  }

  // Enable encryption for a chat room
  Future<bool> enableChatRoomEncryption(
    String chatRoomId, {
    String? customPassword,
  }) async {
    try {
      // Get chat room to check if it exists and get participants
      DocumentSnapshot chatRoomDoc =
          await _chatRoomsCollection.doc(chatRoomId).get();
      if (!chatRoomDoc.exists) {
        print('💬 [ENABLE_ENCRYPTION] Chat room does not exist!');
        return false;
      }

      ChatRoom chatRoom = ChatRoom.fromMap(
        chatRoomDoc.data() as Map<String, dynamic>,
        chatRoomId,
      );

      // Enable encryption using EncryptionService
      bool success = await _encryptionService.enableEncryptionForChatRoom(
        chatRoomId,
        chatRoom.participants,
        customPassword: customPassword,
      );

      if (success) {
        print(
          '💬 [ENABLE_ENCRYPTION] Encryption enabled for chat room: $chatRoomId',
        );
      } else {
        print(
          '💬 [ENABLE_ENCRYPTION] Failed to enable encryption for chat room: $chatRoomId',
        );
      }

      return success;
    } catch (e) {
      print('💬 [ENABLE_ENCRYPTION] Error enabling encryption: $e');
      return false;
    }
  }

  // Disable encryption for a chat room
  Future<bool> disableChatRoomEncryption(String chatRoomId) async {
    try {
      bool success = await _encryptionService.disableEncryptionForChatRoom(
        chatRoomId,
      );

      if (success) {
        print(
          '💬 [DISABLE_ENCRYPTION] Encryption disabled for chat room: $chatRoomId',
        );
      } else {
        print(
          '💬 [DISABLE_ENCRYPTION] Failed to disable encryption for chat room: $chatRoomId',
        );
      }

      return success;
    } catch (e) {
      print('💬 [DISABLE_ENCRYPTION] Error disabling encryption: $e');
      return false;
    }
  }

  // Get chat rooms for current user with optimization to prevent unnecessary rebuilds
  Stream<List<ChatRoom>> getChatRooms() {
    if (currentUserId.isEmpty) {
      return Stream.value([]);
    }

    return _chatRoomsCollection
        .where('participants', arrayContains: currentUserId)
        .orderBy('lastMessageTime', descending: true)
        .snapshots()
        .map((snapshot) {
          return snapshot.docs.map((doc) {
            return ChatRoom.fromMap(doc.data() as Map<String, dynamic>, doc.id);
          }).toList();
        })
        .distinct((previous, current) {
          // Only emit if the lists are actually different
          if (previous.length != current.length) return false;
          for (int i = 0; i < previous.length; i++) {
            if (previous[i] != current[i]) return false;
          }
          return true;
        });
  }

  // Get real-time stream for a specific chat room
  Stream<ChatRoom> getChatRoomStream(String chatRoomId) {
    return _chatRoomsCollection.doc(chatRoomId).snapshots().map((snapshot) {
      if (!snapshot.exists) {
        throw Exception('Chat room not found');
      }
      return ChatRoom.fromMap(
        snapshot.data() as Map<String, dynamic>,
        snapshot.id,
      );
    });
  }

  // Get chat room by ID
  Future<ChatRoom?> getChatRoomById(String chatRoomId) async {
    try {
      DocumentSnapshot doc = await _chatRoomsCollection.doc(chatRoomId).get();
      if (doc.exists) {
        return ChatRoom.fromMap(doc.data() as Map<String, dynamic>, doc.id);
      }
      return null;
    } catch (e) {
      debugPrint('Error getting chat room by ID: $e');
      return null;
    }
  }

  // Send message
  Future<String?> sendMessage({
    // Changed return type to Future<String?>
    required String chatRoomId,
    required String text,
    MessageType type = MessageType.text,
    String? mediaUrl,
    Map<String, dynamic>? metadata,
  }) async {
    print('💬 [SEND_MESSAGE] Called with type: $type, text: $text');
    print(
      '💬 [SEND_MESSAGE] chatRoomId: $chatRoomId, currentUserId: $currentUserId',
    );
    print('💬 [SEND_MESSAGE] metadata: $metadata');

    if (currentUserId.isEmpty || chatRoomId.isEmpty) {
      print('💬 [SEND_MESSAGE] Invalid parameters, returning early');
      return null; // Return null if parameters are invalid
    }

    print('💬 [SEND_MESSAGE] Getting chat room document...');
    // Get chat room
    DocumentSnapshot chatRoomDoc =
        await _chatRoomsCollection.doc(chatRoomId).get();
    if (!chatRoomDoc.exists) {
      print('💬 [SEND_MESSAGE] Chat room does not exist!');
      return null; // Return null if chat room doesn't exist
    }
    print('💬 [SEND_MESSAGE] Chat room found, proceeding...');

    ChatRoom chatRoom = ChatRoom.fromMap(
      chatRoomDoc.data() as Map<String, dynamic>,
      chatRoomId,
    );

    // Check if chat room is encrypted and handle encryption
    String? encryptedText;
    String? encryptedMediaUrl;
    bool isEncrypted = chatRoom.isEncrypted;

    if (isEncrypted) {
      print('💬 [SEND_MESSAGE] Chat room is encrypted, encrypting message...');
      // Load encryption key for this chat room
      await _encryptionService.loadChatRoomKey(chatRoomId);

      // Encrypt text content if present
      if (text.isNotEmpty) {
        encryptedText = _encryptionService.encryptMessage(chatRoomId, text);
        if (encryptedText == null) {
          print('💬 [SEND_MESSAGE] Failed to encrypt message text');
          return null;
        }
      }

      // Encrypt media URL if present
      if (mediaUrl != null && mediaUrl.isNotEmpty) {
        encryptedMediaUrl = _encryptionService.encryptMessage(
          chatRoomId,
          mediaUrl,
        );
        if (encryptedMediaUrl == null) {
          print('💬 [SEND_MESSAGE] Failed to encrypt media URL');
          return null;
        }
      }
      print('💬 [SEND_MESSAGE] Message encryption completed');
    }

    // Create message
    print('💬 [SEND_MESSAGE] Creating message document...');
    final messageData = {
      'chatRoomId': chatRoomId,
      'senderId': currentUserId,
      'text': isEncrypted ? null : text, // Store null for encrypted messages
      'mediaUrl':
          isEncrypted ? null : mediaUrl, // Store null for encrypted messages
      'type': type.index,
      'status': MessageStatus.sent.index,
      'timestamp': FieldValue.serverTimestamp(),
      'readBy': [currentUserId],
      'metadata': metadata,
      'isEncrypted': isEncrypted,
      'encryptedText': encryptedText,
      'encryptedMediaUrl': encryptedMediaUrl,
    };
    print('💬 [SEND_MESSAGE] Message data: $messageData');

    DocumentReference messageRef = await _messagesCollection.add(
      messageData,
    ); // Get reference
    print(
      '💬 [SEND_MESSAGE] Message document created successfully with ID: ${messageRef.id}',
    );

    // Update unread count for other participants
    Map<String, int> updatedUnreadCount = Map.from(chatRoom.unreadCount);
    for (String userId in chatRoom.participants) {
      if (userId != currentUserId) {
        updatedUnreadCount[userId] = (updatedUnreadCount[userId] ?? 0) + 1;
      }
    }

    // Update chat room with last message
    await _chatRoomsCollection.doc(chatRoomId).update({
      'lastMessage': text,
      'lastMessageTime': FieldValue.serverTimestamp(),
      'unreadCount': updatedUnreadCount,
    });

    // Send notifications to other participants (fire-and-forget for smooth UX)
    _sendNotificationsToParticipants(
      chatRoom: chatRoom,
      messageText: text,
      messageType: type,
    ).catchError((error) {
      // Log error but don't block message sending
      print('🔔 [NOTIFICATION] Error sending notifications: $error');
    });
    return messageRef.id; // Return the message ID
  }

  // Send a system message (e.g., for missed calls)
  Future<void> sendSystemMessage({
    required String chatRoomId,
    required String text,
    required String receiverId, // The user who will see this system message
  }) async {
    print('💬 [SEND_SYSTEM_MESSAGE] Called with text: $text');
    print(
      '💬 [SEND_SYSTEM_MESSAGE] chatRoomId: $chatRoomId, receiverId: $receiverId',
    );

    if (chatRoomId.isEmpty) {
      print('💬 [SEND_SYSTEM_MESSAGE] Invalid chatRoomId, returning early');
      return;
    }

    // Get chat room
    DocumentSnapshot chatRoomDoc =
        await _chatRoomsCollection.doc(chatRoomId).get();
    if (!chatRoomDoc.exists) {
      print('💬 [SEND_SYSTEM_MESSAGE] Chat room does not exist!');
      return;
    }
    ChatRoom chatRoom = ChatRoom.fromMap(
      chatRoomDoc.data() as Map<String, dynamic>,
      chatRoomId,
    );

    // Create system message
    // System messages are sent by a "system" user or a generic ID.
    // For simplicity, we'll mark them with a special senderId or type if needed,
    // but here we'll just send them as a text message from a "system" perspective.
    // The key is that they appear in the chat for the specified receiverId.
    final messageData = {
      'chatRoomId': chatRoomId,
      'senderId': 'system', // Indicates a system message
      'text': text,
      'mediaUrl': null,
      'type': MessageType.text.index, // Treat as a text message for display
      'status': MessageStatus.sent.index, // System messages are considered sent
      'timestamp': FieldValue.serverTimestamp(),
      'readBy':
          [], // System messages might not need read tracking or only for receiver
      'metadata': {'isSystemMessage': true}, // Custom metadata
    };
    print('💬 [SEND_SYSTEM_MESSAGE] Message data: $messageData');

    await _messagesCollection.add(messageData);
    print(
      '💬 [SEND_SYSTEM_MESSAGE] System message document created successfully!',
    );

    // Update chat room with last message (this system message)
    // And update unread count for the specific receiver of this system message
    Map<String, int> updatedUnreadCount = Map.from(chatRoom.unreadCount);
    // Ensure the receiverId is part of the chat room participants before updating unread count
    if (chatRoom.participants.contains(receiverId)) {
      updatedUnreadCount[receiverId] =
          (updatedUnreadCount[receiverId] ?? 0) + 1;
    }

    await _chatRoomsCollection.doc(chatRoomId).update({
      'lastMessage': text, // System message text
      'lastMessageTime': FieldValue.serverTimestamp(),
      'unreadCount': updatedUnreadCount,
    });
    print('💬 [SEND_SYSTEM_MESSAGE] Chat room updated with system message.');
  }

  // Get messages for a chat room with optimization to prevent unnecessary rebuilds and decryption support
  Stream<List<Message>> getMessages(String chatRoomId) {
    if (chatRoomId.isEmpty) {
      return Stream.value([]);
    }

    return _messagesCollection
        .where('chatRoomId', isEqualTo: chatRoomId)
        .orderBy('timestamp', descending: true)
        .snapshots()
        .asyncMap((snapshot) async {
          List<Message> messages = [];

          for (var doc in snapshot.docs) {
            Message message = Message.fromMap(
              doc.data() as Map<String, dynamic>,
              doc.id,
            );

            // If message is encrypted, try to decrypt it
            if (message.isEncrypted) {
              await _encryptionService.loadChatRoomKey(chatRoomId);

              String? decryptedText;
              String? decryptedMediaUrl;

              // Decrypt text if present
              if (message.encryptedText != null) {
                decryptedText = _encryptionService.decryptMessage(
                  chatRoomId,
                  message.encryptedText!,
                );
              }

              // Decrypt media URL if present
              if (message.encryptedMediaUrl != null) {
                decryptedMediaUrl = _encryptionService.decryptMessage(
                  chatRoomId,
                  message.encryptedMediaUrl!,
                );
              }

              // Create a new message with decrypted content for display
              if (decryptedText != null || decryptedMediaUrl != null) {
                message = Message(
                  id: message.id,
                  chatRoomId: message.chatRoomId,
                  senderId: message.senderId,
                  text: decryptedText ?? message.text,
                  mediaUrl: decryptedMediaUrl ?? message.mediaUrl,
                  type: message.type,
                  status: message.status,
                  timestamp: message.timestamp,
                  readBy: message.readBy,
                  metadata: message.metadata,
                  isEncrypted: message.isEncrypted,
                  encryptedText: message.encryptedText,
                  encryptedMediaUrl: message.encryptedMediaUrl,
                );
              }
            }

            messages.add(message);
          }

          return messages;
        })
        .distinct((previous, current) {
          // Only emit if the lists are actually different
          if (previous.length != current.length) return false;
          for (int i = 0; i < previous.length; i++) {
            if (previous[i] != current[i]) return false;
          }
          return true;
        });
  }

  // Get messages with pagination (15 messages per page) and decryption support
  Stream<List<Message>> getMessagesPaginated(
    String chatRoomId, {
    int limit = 15,
  }) {
    if (chatRoomId.isEmpty) {
      return Stream.value([]);
    }

    return _messagesCollection
        .where('chatRoomId', isEqualTo: chatRoomId)
        .orderBy('timestamp', descending: true)
        .limit(limit)
        .snapshots()
        .asyncMap((snapshot) async {
          List<Message> messages = [];

          for (var doc in snapshot.docs) {
            Message message = Message.fromMap(
              doc.data() as Map<String, dynamic>,
              doc.id,
            );

            // If message is encrypted, try to decrypt it
            if (message.isEncrypted) {
              await _encryptionService.loadChatRoomKey(chatRoomId);

              String? decryptedText;
              String? decryptedMediaUrl;

              // Decrypt text if present
              if (message.encryptedText != null) {
                decryptedText = _encryptionService.decryptMessage(
                  chatRoomId,
                  message.encryptedText!,
                );
              }

              // Decrypt media URL if present
              if (message.encryptedMediaUrl != null) {
                decryptedMediaUrl = _encryptionService.decryptMessage(
                  chatRoomId,
                  message.encryptedMediaUrl!,
                );
              }

              // Create a new message with decrypted content for display
              if (decryptedText != null || decryptedMediaUrl != null) {
                message = Message(
                  id: message.id,
                  chatRoomId: message.chatRoomId,
                  senderId: message.senderId,
                  text: decryptedText ?? message.text,
                  mediaUrl: decryptedMediaUrl ?? message.mediaUrl,
                  type: message.type,
                  status: message.status,
                  timestamp: message.timestamp,
                  readBy: message.readBy,
                  metadata: message.metadata,
                  isEncrypted: message.isEncrypted,
                  encryptedText: message.encryptedText,
                  encryptedMediaUrl: message.encryptedMediaUrl,
                );
              }
            }

            messages.add(message);
          }

          return messages;
        })
        .distinct((previous, current) {
          // Only emit if the lists are actually different
          if (previous.length != current.length) return false;
          for (int i = 0; i < previous.length; i++) {
            if (previous[i] != current[i]) return false;
          }
          return true;
        });
  }

  // Load more messages for pagination
  Future<List<Message>> loadMoreMessages(
    String chatRoomId,
    DateTime lastMessageTimestamp, {
    int limit = 15,
  }) async {
    if (chatRoomId.isEmpty) {
      return [];
    }

    try {
      final QuerySnapshot snapshot =
          await _messagesCollection
              .where('chatRoomId', isEqualTo: chatRoomId)
              .where(
                'timestamp',
                isLessThan: Timestamp.fromDate(lastMessageTimestamp),
              )
              .orderBy('timestamp', descending: true)
              .limit(limit)
              .get();

      return snapshot.docs.map((doc) {
        return Message.fromMap(doc.data() as Map<String, dynamic>, doc.id);
      }).toList();
    } catch (e) {
      debugPrint('Error loading more messages: $e');
      return [];
    }
  }

  // Mark messages as read - Fixed version
  Future<void> markMessagesAsRead(String chatRoomId) async {
    if (currentUserId.isEmpty || chatRoomId.isEmpty) return;

    try {
      // IMMEDIATE: Reset unread count to 0 first (zero delay for UI)
      await _chatRoomsCollection.doc(chatRoomId).update({
        'unreadCount.$currentUserId': 0,
      });

      // BACKGROUND: Get all messages from others and mark as read
      final allMessages =
          await _messagesCollection
              .where('chatRoomId', isEqualTo: chatRoomId)
              .where('senderId', isNotEqualTo: currentUserId)
              .get();

      if (allMessages.docs.isNotEmpty) {
        final batch = _firestore.batch();

        for (var doc in allMessages.docs) {
          final data = doc.data() as Map<String, dynamic>;
          final readBy = List<String>.from(data['readBy'] ?? []);

          // If current user hasn't read this message yet
          if (!readBy.contains(currentUserId)) {
            batch.update(doc.reference, {
              'readBy': FieldValue.arrayUnion([currentUserId]),
              'status': MessageStatus.read.index,
            });
          }
        }

        await batch.commit();
      }
    } catch (e) {
      // Silent fallback - don't break the UI
      debugPrint('Error marking messages as read: $e');
    }
  }

  // Delete message
  Future<void> deleteMessage(String messageId) async {
    await _messagesCollection.doc(messageId).delete();
  }

  // Delete chat room
  Future<void> deleteChatRoom(String chatRoomId) async {
    // Delete all messages in the chat room
    QuerySnapshot messages =
        await _messagesCollection
            .where('chatRoomId', isEqualTo: chatRoomId)
            .get();

    WriteBatch batch = _firestore.batch();
    for (var doc in messages.docs) {
      batch.delete(doc.reference);
    }

    // Delete the chat room
    batch.delete(_chatRoomsCollection.doc(chatRoomId));

    await batch.commit();
  }

  // Upload image and send message
  Future<void> sendImageMessage({
    required String chatRoomId,
    required XFile imageFile,
    String? caption,
    Function(double)? onProgress,
  }) async {
    if (currentUserId.isEmpty || chatRoomId.isEmpty) return;

    try {
      final File file = File(imageFile.path);
      final int fileSize = await file.length();
      final String fileName =
          '${DateTime.now().millisecondsSinceEpoch}_${imageFile.name}';

      // STEP 1: Create message immediately with loading state
      final messageDoc = await _messagesCollection.add({
        'chatRoomId': chatRoomId,
        'senderId': currentUserId,
        'text': caption ?? '📷 Photo',
        'mediaUrl': null, // Will be updated after upload
        'type': MessageType.image.index,
        'status': MessageStatus.sending.index,
        'timestamp': FieldValue.serverTimestamp(),
        'readBy': [currentUserId],
        'metadata': {
          'fileName': imageFile.name,
          'fileSize': fileSize,
          'fileExtension': imageFile.path.split('.').last.toLowerCase(),
          'mimeType': lookupMimeType(imageFile.path),
          'isUploading': true, // Flag to show loading state
          'localPath': imageFile.path, // Store local path for preview
        },
      });

      // STEP 2: Update chat room immediately
      final chatRoomDoc = await _chatRoomsCollection.doc(chatRoomId).get();
      if (chatRoomDoc.exists) {
        final chatRoom = ChatRoom.fromMap(
          chatRoomDoc.data() as Map<String, dynamic>,
          chatRoomId,
        );

        // Update unread count for other participants
        Map<String, int> updatedUnreadCount = Map.from(chatRoom.unreadCount);
        for (String userId in chatRoom.participants) {
          if (userId != currentUserId) {
            updatedUnreadCount[userId] = (updatedUnreadCount[userId] ?? 0) + 1;
          }
        }

        await _chatRoomsCollection.doc(chatRoomId).update({
          'lastMessage': caption ?? '📷 Photo',
          'lastMessageTime': FieldValue.serverTimestamp(),
          'unreadCount': updatedUnreadCount,
        });

        // Send notifications to other participants (fire-and-forget for smooth UX)
        _sendNotificationsToParticipants(
          chatRoom: chatRoom,
          messageText: caption ?? '📷 Photo',
          messageType: MessageType.image,
        ).catchError((error) {
          // Log error but don't block message sending
          print('🔔 [NOTIFICATION] Error sending image notifications: $error');
        });
      }

      // STEP 3: Upload image to Firebase Storage
      final Reference storageRef = _storage
          .ref()
          .child('chat_images')
          .child(currentUserId)
          .child(fileName);

      final UploadTask uploadTask = storageRef.putFile(file);

      // Listen to upload progress
      uploadTask.snapshotEvents.listen((TaskSnapshot snapshot) {
        final progress = snapshot.bytesTransferred / snapshot.totalBytes;
        onProgress?.call(progress);
      });

      // STEP 4: Update message when upload completes
      final TaskSnapshot snapshot = await uploadTask;
      final String downloadUrl = await snapshot.ref.getDownloadURL();

      // Update message with download URL and sent status
      await messageDoc.update({
        'mediaUrl': downloadUrl,
        'status': MessageStatus.sent.index,
        'metadata': {
          'fileName': imageFile.name,
          'fileSize': fileSize,
          'fileExtension': imageFile.path.split('.').last.toLowerCase(),
          'mimeType': lookupMimeType(imageFile.path),
          'isUploading': false, // Upload complete
        },
      });
    } catch (e) {
      throw Exception('Failed to upload image: $e');
    }
  }

  // Upload file and send message
  Future<void> sendFileMessage({
    required String chatRoomId,
    required PlatformFile file,
    Function(double)? onProgress,
  }) async {
    if (currentUserId.isEmpty || chatRoomId.isEmpty) return;

    try {
      // Upload file to Firebase Storage
      final String fileName =
          '${DateTime.now().millisecondsSinceEpoch}_${file.name}';
      final Reference storageRef = _storage
          .ref()
          .child('chat_files')
          .child(currentUserId)
          .child(fileName);

      final UploadTask uploadTask = storageRef.putFile(File(file.path!));

      // Listen to upload progress
      uploadTask.snapshotEvents.listen((TaskSnapshot snapshot) {
        final progress = snapshot.bytesTransferred / snapshot.totalBytes;
        onProgress?.call(progress);
      });

      // Wait for upload to complete
      final TaskSnapshot snapshot = await uploadTask;
      final String downloadUrl = await snapshot.ref.getDownloadURL();

      // Determine message type based on file extension
      MessageType messageType = _getMessageTypeFromExtension(
        file.extension ?? '',
      );

      // Send message with file
      await sendMessage(
        chatRoomId: chatRoomId,
        text: '📄 ${file.name}', // User-friendly message for chat list
        type: messageType,
        mediaUrl: downloadUrl,
        metadata: {
          'fileName': file.name,
          'fileSize': file.size,
          'fileExtension': file.extension?.toLowerCase() ?? '',
          'mimeType': lookupMimeType(file.path!),
        },
      );
    } catch (e) {
      throw Exception('Failed to upload file: $e');
    }
  }

  // Upload voice message and send
  Future<void> sendVoiceMessage({
    required String chatRoomId,
    required String audioPath,
    Function(double)? onProgress,
  }) async {
    if (currentUserId.isEmpty || chatRoomId.isEmpty) return;

    DocumentReference? messageDoc;

    try {
      final File audioFile = File(audioPath);
      final int fileSize = await audioFile.length();
      final String fileName =
          '${DateTime.now().millisecondsSinceEpoch}_voice.m4a';

      // STEP 1: Create message immediately with sending status
      messageDoc = await _messagesCollection.add({
        'chatRoomId': chatRoomId,
        'senderId': currentUserId,
        'text': 'Voice message',
        'mediaUrl': null, // Will be updated after upload
        'type': MessageType.audio.index,
        'status': MessageStatus.sending.index, // Show as sending
        'timestamp': FieldValue.serverTimestamp(),
        'readBy': [currentUserId],
        'metadata': {
          'fileName': fileName,
          'fileSize': fileSize,
          'fileExtension': 'm4a',
          'mimeType': 'audio/mp4',
          'duration': 0,
          'isUploading': true, // Flag to show loading state
        },
      });

      // STEP 2: Update chat room immediately
      final chatRoomDoc = await _chatRoomsCollection.doc(chatRoomId).get();
      if (chatRoomDoc.exists) {
        final chatRoom = ChatRoom.fromMap(
          chatRoomDoc.data() as Map<String, dynamic>,
          chatRoomId,
        );

        // Update unread count for other participants
        Map<String, int> updatedUnreadCount = Map.from(chatRoom.unreadCount);
        for (String userId in chatRoom.participants) {
          if (userId != currentUserId) {
            updatedUnreadCount[userId] = (updatedUnreadCount[userId] ?? 0) + 1;
          }
        }

        await _chatRoomsCollection.doc(chatRoomId).update({
          'lastMessage': 'Voice message',
          'lastMessageTime': FieldValue.serverTimestamp(),
          'unreadCount': updatedUnreadCount,
        });

        // Send notifications to other participants (fire-and-forget for smooth UX)
        _sendNotificationsToParticipants(
          chatRoom: chatRoom,
          messageText: 'Voice message',
          messageType: MessageType.audio,
        ).catchError((error) {
          // Log error but don't block message sending
          print('🔔 [NOTIFICATION] Error sending voice notifications: $error');
        });
      }

      // STEP 3: Upload audio file in background
      final Reference storageRef = _storage
          .ref()
          .child('chat_audio')
          .child(currentUserId)
          .child(fileName);

      final UploadTask uploadTask = storageRef.putFile(audioFile);

      // Listen to upload progress
      uploadTask.snapshotEvents.listen((TaskSnapshot snapshot) {
        final progress = snapshot.bytesTransferred / snapshot.totalBytes;
        onProgress?.call(progress);
      });

      // STEP 4: Update message when upload completes
      final TaskSnapshot snapshot = await uploadTask;
      final String downloadUrl = await snapshot.ref.getDownloadURL();

      // Update message with download URL and sent status
      await messageDoc.update({
        'mediaUrl': downloadUrl,
        'status': MessageStatus.sent.index,
        'metadata': {
          'fileName': fileName,
          'fileSize': fileSize,
          'fileExtension': 'm4a',
          'mimeType': 'audio/mp4',
          'duration': 0,
          'isUploading': false, // Upload complete
        },
      });

      // Clean up temporary file
      try {
        await audioFile.delete();
      } catch (e) {
        // Ignore cleanup errors
      }
    } catch (e) {
      // If upload fails, update message status to failed
      // Note: We'd need to track the message ID to update it on failure
      throw Exception('Failed to upload voice message: $e');
    }
  }

  // Upload video message and send
  Future<void> sendVideoMessage({
    required String chatRoomId,
    required XFile videoFile,
    Function(double)? onProgress,
  }) async {
    if (currentUserId.isEmpty || chatRoomId.isEmpty) return;

    try {
      final File file = File(videoFile.path);
      final int fileSize = await file.length();
      final String fileName =
          '${DateTime.now().millisecondsSinceEpoch}_${videoFile.name}';

      // STEP 1: Create message immediately with loading state
      final messageDoc = await _messagesCollection.add({
        'chatRoomId': chatRoomId,
        'senderId': currentUserId,
        'text': '🎥 Video',
        'mediaUrl': null, // Will be updated after upload
        'type': MessageType.video.index,
        'status': MessageStatus.sending.index,
        'timestamp': FieldValue.serverTimestamp(),
        'readBy': [currentUserId],
        'metadata': {
          'fileName': videoFile.name,
          'fileSize': fileSize,
          'fileExtension': videoFile.path.split('.').last.toLowerCase(),
          'mimeType': lookupMimeType(videoFile.path),
          'isUploading': true, // Flag to show loading state
          'localPath': videoFile.path, // Store local path for preview
        },
      });

      // STEP 2: Update chat room immediately
      final chatRoomDoc = await _chatRoomsCollection.doc(chatRoomId).get();
      if (chatRoomDoc.exists) {
        final chatRoom = ChatRoom.fromMap(
          chatRoomDoc.data() as Map<String, dynamic>,
          chatRoomId,
        );

        // Update unread count for other participants
        Map<String, int> updatedUnreadCount = Map.from(chatRoom.unreadCount);
        for (String userId in chatRoom.participants) {
          if (userId != currentUserId) {
            updatedUnreadCount[userId] = (updatedUnreadCount[userId] ?? 0) + 1;
          }
        }

        await _chatRoomsCollection.doc(chatRoomId).update({
          'lastMessage': '🎥 Video',
          'lastMessageTime': FieldValue.serverTimestamp(),
          'unreadCount': updatedUnreadCount,
        });

        // Send notifications to other participants (fire-and-forget for smooth UX)
        _sendNotificationsToParticipants(
          chatRoom: chatRoom,
          messageText: '🎥 Video',
          messageType: MessageType.video,
        ).catchError((error) {
          // Log error but don't block message sending
          print('🔔 [NOTIFICATION] Error sending video notifications: $error');
        });
      }

      // STEP 3: Upload video file to Firebase Storage
      final Reference storageRef = _storage
          .ref()
          .child('chat_videos')
          .child(currentUserId)
          .child(fileName);

      final UploadTask uploadTask = storageRef.putFile(file);

      // Listen to upload progress
      uploadTask.snapshotEvents.listen((TaskSnapshot snapshot) {
        final progress = snapshot.bytesTransferred / snapshot.totalBytes;
        onProgress?.call(progress);
      });

      // STEP 4: Update message when upload completes
      final TaskSnapshot snapshot = await uploadTask;
      final String downloadUrl = await snapshot.ref.getDownloadURL();

      // Update message with download URL and sent status
      await messageDoc.update({
        'mediaUrl': downloadUrl,
        'status': MessageStatus.sent.index,
        'metadata': {
          'fileName': videoFile.name,
          'fileSize': fileSize,
          'fileExtension': videoFile.path.split('.').last.toLowerCase(),
          'mimeType': lookupMimeType(videoFile.path),
          'isUploading': false, // Upload complete
        },
      });
    } catch (e) {
      throw Exception('Failed to upload video: $e');
    }
  }

  // Send location message
  Future<void> sendLocationMessage({
    required String chatRoomId,
    required Map<String, dynamic> locationData,
  }) async {
    print('🔥 [CHAT_SERVICE] sendLocationMessage called');
    print('🔥 [CHAT_SERVICE] chatRoomId: $chatRoomId');
    print('🔥 [CHAT_SERVICE] locationData: $locationData');
    print('🔥 [CHAT_SERVICE] currentUserId: $currentUserId');

    if (currentUserId.isEmpty || chatRoomId.isEmpty) {
      print(
        '🔥 [CHAT_SERVICE] Invalid parameters - currentUserId: $currentUserId, chatRoomId: $chatRoomId',
      );
      return;
    }

    try {
      print('🔥 [CHAT_SERVICE] Preparing location metadata...');
      final metadata = {
        'latitude': locationData['latitude'],
        'longitude': locationData['longitude'],
        'address': locationData['address'],
        'timestamp': locationData['timestamp'],
      };
      print('🔥 [CHAT_SERVICE] Location metadata: $metadata');

      print('🔥 [CHAT_SERVICE] Calling sendMessage with location data...');
      // Send message with location
      await sendMessage(
        chatRoomId: chatRoomId,
        text: '📍 Shared location', // User-friendly message for chat list
        type: MessageType.location,
        metadata: metadata,
      );
      print('🔥 [CHAT_SERVICE] Location message sent successfully!');
    } catch (e) {
      print('🔥 [CHAT_SERVICE] Failed to send location message: $e');
      throw Exception('Failed to send location: $e');
    }
  }

  // Helper method to determine message type from file extension
  MessageType _getMessageTypeFromExtension(String extension) {
    switch (extension.toLowerCase()) {
      case '.jpg':
      case '.jpeg':
      case '.png':
      case '.gif':
      case '.webp':
        return MessageType.image;
      case '.mp4':
      case '.avi':
      case '.mov':
      case '.wmv':
      case '.flv':
        return MessageType.video;
      case '.mp3':
      case '.wav':
      case '.aac':
      case '.flac':
      case '.ogg':
      case '.m4a':
        return MessageType.audio;
      default:
        return MessageType.file;
    }
  }

  // Send notifications to other participants
  Future<void> _sendNotificationsToParticipants({
    required ChatRoom chatRoom,
    required String messageText,
    required MessageType messageType,
  }) async {
    // Suppress notification for call messages
    if (messageType == MessageType.audioCall ||
        messageType == MessageType.videoCall) {
      return;
    }
    try {
      // Get current user info for notification
      final currentUser = await getUserById(currentUserId);
      if (currentUser == null) return;

      // Send notification to each participant (except sender)
      for (String participantId in chatRoom.participants) {
        if (participantId != currentUserId) {
          // Get participant info
          final participant = await getUserById(participantId);
          if (participant == null) continue;

          // Create notification title and body
          String notificationTitle =
              currentUser.name ?? currentUser.phoneNumber ?? 'Someone';
          String notificationBody = _getNotificationBody(
            messageText,
            messageType,
          );

          // Send notification
          await _notificationService.sendNotificationToUser(
            userId: participantId,
            title: notificationTitle,
            body: notificationBody,
            data: {
              'chatRoomId': chatRoom.id,
              'senderId': currentUserId,
              'senderName': notificationTitle,
              'messageType': messageType.name,
              'type': 'new_message',
            },
          );
        }
      }
    } catch (e) {
      print('🔔 [NOTIFICATION] Error sending notifications: $e');
      // Don't throw error - notifications are not critical for message sending
    }
  }

  // Get user-friendly notification body based on message type
  String _getNotificationBody(String messageText, MessageType messageType) {
    switch (messageType) {
      case MessageType.text:
        return messageText.length > 50
            ? '${messageText.substring(0, 50)}...'
            : messageText;
      case MessageType.image:
        return '📷 Photo';
      case MessageType.video:
        return '🎥 Video';
      case MessageType.audio:
        return '🎵 Voice message';
      case MessageType.file:
        return '📄 File';
      case MessageType.location:
        return '📍 Location';
      default:
        return 'New message';
    }
  }
}
