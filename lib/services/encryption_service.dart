import 'dart:convert';
import 'dart:math';
import 'package:crypto/crypto.dart';
import 'package:encrypt/encrypt.dart' as encrypt;
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter/foundation.dart';
import 'package:tolk/models/chat_models.dart';

class EncryptionService {
  static final EncryptionService _instance = EncryptionService._internal();
  factory EncryptionService() => _instance;
  EncryptionService._internal();

  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;

  // Collections
  CollectionReference? _chatRoomsCollection;

  // Chat room encryption keys (AES keys for each encrypted chat)
  final Map<String, String> _chatRoomKeys = {}; // chatRoomId -> AES key
  final Map<String, String> _chatRoomPasswords = {}; // chatRoomId -> password

  String get currentUserId => _auth.currentUser?.uid ?? '';

  void initialize() {
    _chatRoomsCollection ??= _firestore.collection('chatRooms');
  }

  CollectionReference get chatRoomsCollection {
    if (_chatRoomsCollection == null) {
      initialize();
    }
    return _chatRoomsCollection!;
  }

  /// Generate a secure password for chat room encryption
  String _generateSecurePassword() {
    const chars =
        'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#\$%^&*';
    final random = Random.secure();
    return List.generate(
      32,
      (index) => chars[random.nextInt(chars.length)],
    ).join();
  }

  /// Generate AES key from password
  encrypt.Key _generateKeyFromPassword(String password) {
    final bytes = utf8.encode(password);
    final digest = sha256.convert(bytes);
    return encrypt.Key.fromBase64(base64.encode(digest.bytes));
  }

  /// Enable encryption for a chat room with a shared password
  Future<bool> enableEncryptionForChatRoom(
    String chatRoomId,
    List<String> participantIds, {
    String? customPassword,
  }) async {
    try {
      // Generate or use custom password
      final password = customPassword ?? _generateSecurePassword();
      final aesKey = _generateKeyFromPassword(password);

      // Store keys in memory
      _chatRoomKeys[chatRoomId] = aesKey.base64;
      _chatRoomPasswords[chatRoomId] = password;

      // Store encrypted password locally for current user
      await _storePasswordLocally(chatRoomId, password);

      // Update chat room with encryption info
      await chatRoomsCollection.doc(chatRoomId).update({
        'isEncrypted': true,
        'encryptionEnabledAt': FieldValue.serverTimestamp(),
        'encryptionEnabledBy': currentUserId,
        'encryptionPassword':
            password, // In real app, this should be shared securely
      });

      debugPrint('🔐 Encryption enabled for chat room: $chatRoomId');
      return true;
    } catch (e) {
      debugPrint('❌ Error enabling encryption for chat room: $e');
      return false;
    }
  }

  /// Disable encryption for a chat room
  Future<bool> disableEncryptionForChatRoom(String chatRoomId) async {
    try {
      // Check if the last message was encrypted and update lastMessage field
      await _updateLastMessageForDisabledEncryption(chatRoomId);

      // Remove keys from memory
      _chatRoomKeys.remove(chatRoomId);
      _chatRoomPasswords.remove(chatRoomId);

      // Remove password from local storage
      await _removePasswordLocally(chatRoomId);

      // Update chat room to disable encryption
      await chatRoomsCollection.doc(chatRoomId).update({
        'isEncrypted': false,
        'encryptionPassword': FieldValue.delete(),
        'encryptionDisabledAt': FieldValue.serverTimestamp(),
        'encryptionDisabledBy': currentUserId,
      });

      debugPrint('🔐 Encryption disabled for chat room: $chatRoomId');
      return true;
    } catch (e) {
      debugPrint('❌ Error disabling encryption for chat room: $e');
      return false;
    }
  }

  /// Clear encryption key from memory for a chat room
  void clearChatRoomKey(String chatRoomId) {
    _chatRoomKeys.remove(chatRoomId);
    _chatRoomPasswords.remove(chatRoomId);
    debugPrint(
      '🔐 Encryption key cleared from memory for chat room: $chatRoomId',
    );
  }

  /// Store password locally with device encryption
  Future<void> _storePasswordLocally(String chatRoomId, String password) async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // Create a device-specific encryption key
      final deviceKey = await _getOrCreateDeviceKey();
      final encrypter = encrypt.Encrypter(
        encrypt.AES(encrypt.Key.fromBase64(deviceKey)),
      );

      // Generate a random IV for this encryption
      final iv = encrypt.IV.fromSecureRandom(16);

      // Encrypt and store password with IV
      final encrypted = encrypter.encrypt(password, iv: iv);

      // Store both encrypted data and IV
      await prefs.setString('chat_password_$chatRoomId', encrypted.base64);
      await prefs.setString('chat_password_iv_$chatRoomId', iv.base64);

      debugPrint('🔐 Password stored locally for chat room: $chatRoomId');
    } catch (e) {
      debugPrint('❌ Error storing password locally: $e');
      rethrow;
    }
  }

  /// Remove password from local storage
  Future<void> _removePasswordLocally(String chatRoomId) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove('chat_password_$chatRoomId');
      debugPrint('🔐 Password removed locally for chat room: $chatRoomId');
    } catch (e) {
      debugPrint('❌ Error removing password locally: $e');
    }
  }

  /// Get or create device-specific encryption key
  Future<String> _getOrCreateDeviceKey() async {
    final prefs = await SharedPreferences.getInstance();
    String? deviceKey = prefs.getString('device_encryption_key');

    if (deviceKey == null) {
      // Generate new device key
      final key = encrypt.Key.fromSecureRandom(32);
      deviceKey = key.base64;
      await prefs.setString('device_encryption_key', deviceKey);
      debugPrint('🔐 New device encryption key generated');
    }

    return deviceKey;
  }

  /// Load chat room encryption key
  Future<bool> loadChatRoomKey(String chatRoomId) async {
    if (currentUserId.isEmpty) return false;

    try {
      // Check if already loaded
      if (_chatRoomKeys.containsKey(chatRoomId)) return true;

      final chatRoomDoc = await chatRoomsCollection.doc(chatRoomId).get();
      if (!chatRoomDoc.exists) return false;

      final data = chatRoomDoc.data() as Map<String, dynamic>;
      final isEncrypted = data['isEncrypted'] ?? false;

      if (!isEncrypted) return true; // Not encrypted, no key needed

      // Try to load password from local storage first
      String? password = await _loadPasswordLocally(chatRoomId);

      // If not found locally, get from Firestore (temporary solution)
      if (password == null) {
        password = data['encryptionPassword'] as String?;
        if (password != null) {
          await _storePasswordLocally(chatRoomId, password);
        }
      }

      if (password == null) {
        debugPrint('❌ No encryption password found for chat room: $chatRoomId');
        return false;
      }

      // Generate AES key from password
      final aesKey = _generateKeyFromPassword(password);
      _chatRoomKeys[chatRoomId] = aesKey.base64;
      _chatRoomPasswords[chatRoomId] = password;

      debugPrint('🔐 Chat room key loaded successfully for: $chatRoomId');
      return true;
    } catch (e) {
      debugPrint('❌ Error loading chat room key: $e');
      return false;
    }
  }

  /// Load password from local storage
  Future<String?> _loadPasswordLocally(String chatRoomId) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final encryptedPassword = prefs.getString('chat_password_$chatRoomId');
      final ivString = prefs.getString('chat_password_iv_$chatRoomId');

      if (encryptedPassword == null || ivString == null) return null;

      // Decrypt password using stored IV
      final deviceKey = await _getOrCreateDeviceKey();
      final encrypter = encrypt.Encrypter(
        encrypt.AES(encrypt.Key.fromBase64(deviceKey)),
      );
      final iv = encrypt.IV.fromBase64(ivString);
      final decrypted = encrypter.decrypt64(encryptedPassword, iv: iv);

      return decrypted;
    } catch (e) {
      debugPrint('❌ Error loading password locally: $e');
      return null;
    }
  }

  /// Encrypt message content
  String? encryptMessage(String chatRoomId, String message) {
    try {
      final aesKey = _chatRoomKeys[chatRoomId];
      if (aesKey == null) {
        debugPrint('❌ No encryption key found for chat room: $chatRoomId');
        return null;
      }

      final encrypter = encrypt.Encrypter(
        encrypt.AES(encrypt.Key.fromBase64(aesKey)),
      );

      // Generate a random IV for each message
      final iv = encrypt.IV.fromSecureRandom(16);
      final encrypted = encrypter.encrypt(message, iv: iv);

      // Combine IV and encrypted data (IV + encrypted message)
      final combined = '${iv.base64}:${encrypted.base64}';
      return combined;
    } catch (e) {
      debugPrint('❌ Error encrypting message: $e');
      return null;
    }
  }

  /// Decrypt message content
  String? decryptMessage(String chatRoomId, String encryptedMessage) {
    try {
      final aesKey = _chatRoomKeys[chatRoomId];
      if (aesKey == null) {
        debugPrint('❌ No encryption key found for chat room: $chatRoomId');
        return null;
      }

      // Split IV and encrypted data
      final parts = encryptedMessage.split(':');
      if (parts.length != 2) {
        debugPrint('❌ Invalid encrypted message format');
        return null;
      }

      final encrypter = encrypt.Encrypter(
        encrypt.AES(encrypt.Key.fromBase64(aesKey)),
      );

      // Decrypt using the stored IV
      final iv = encrypt.IV.fromBase64(parts[0]);
      final encrypted = encrypt.Encrypted.fromBase64(parts[1]);
      final decrypted = encrypter.decrypt(encrypted, iv: iv);
      return decrypted;
    } catch (e) {
      debugPrint('❌ Error decrypting message: $e');
      return null;
    }
  }

  /// Check if chat room is encrypted
  Future<bool> isChatRoomEncrypted(String chatRoomId) async {
    try {
      final doc = await chatRoomsCollection.doc(chatRoomId).get();
      if (!doc.exists) return false;

      final data = doc.data() as Map<String, dynamic>;
      return data['isEncrypted'] ?? false;
    } catch (e) {
      debugPrint('❌ Error checking chat room encryption status: $e');
      return false;
    }
  }

  /// Get encryption password for sharing (for manual key exchange)
  String? getChatRoomPassword(String chatRoomId) {
    return _chatRoomPasswords[chatRoomId];
  }

  /// Set encryption password manually (for joining encrypted chat)
  Future<bool> setChatRoomPassword(String chatRoomId, String password) async {
    try {
      final aesKey = _generateKeyFromPassword(password);
      _chatRoomKeys[chatRoomId] = aesKey.base64;
      _chatRoomPasswords[chatRoomId] = password;

      await _storePasswordLocally(chatRoomId, password);

      debugPrint('🔐 Chat room password set for: $chatRoomId');
      return true;
    } catch (e) {
      debugPrint('❌ Error setting chat room password: $e');
      return false;
    }
  }

  /// Clear all encryption data (for logout)
  void clearEncryptionData() {
    _chatRoomKeys.clear();
    _chatRoomPasswords.clear();
    debugPrint('🔐 Encryption data cleared');
  }

  /// Check if chat room has encryption key loaded
  bool isChatRoomKeyLoaded(String chatRoomId) {
    return _chatRoomKeys.containsKey(chatRoomId);
  }

  /// Update lastMessage field when encryption is disabled
  Future<void> _updateLastMessageForDisabledEncryption(
    String chatRoomId,
  ) async {
    try {
      // Get the most recent message for this chat room
      final messagesQuery =
          await _firestore
              .collection('messages')
              .where('chatRoomId', isEqualTo: chatRoomId)
              .orderBy('timestamp', descending: true)
              .limit(1)
              .get();

      if (messagesQuery.docs.isNotEmpty) {
        final lastMessageDoc = messagesQuery.docs.first;
        final lastMessageData = lastMessageDoc.data();
        final isEncrypted = lastMessageData['isEncrypted'] ?? false;

        if (isEncrypted) {
          // The last message is encrypted, so update the chat room's lastMessage field
          String encryptedMessageText;
          final messageType = MessageType.values[lastMessageData['type'] ?? 0];

          switch (messageType) {
            case MessageType.text:
              encryptedMessageText = 'This message is encrypted';
              break;
            case MessageType.image:
              encryptedMessageText = '🔒 Encrypted image';
              break;
            case MessageType.video:
              encryptedMessageText = '🔒 Encrypted video';
              break;
            case MessageType.audio:
              encryptedMessageText = '🔒 Encrypted voice message';
              break;
            case MessageType.file:
              encryptedMessageText = '🔒 Encrypted file';
              break;
            case MessageType.location:
              encryptedMessageText = '🔒 Encrypted location';
              break;
            default:
              encryptedMessageText = 'This message is encrypted';
              break;
          }

          await chatRoomsCollection.doc(chatRoomId).update({
            'lastMessage': encryptedMessageText,
          });

          debugPrint(
            '🔐 Updated lastMessage for disabled encryption: $encryptedMessageText',
          );
        }
      }
    } catch (e) {
      debugPrint('❌ Error updating lastMessage for disabled encryption: $e');
      // Don't throw error, just log it as this is not critical
    }
  }
}
